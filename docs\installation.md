# Installation Guide

This guide will help you install TTSBook and its dependencies.

## Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- Git (for development installation)

## Installation Methods

### 1. From PyPI (Recommended for Users)

```bash
pip install ttsbook
```

### 2. From Source (Recommended for Developers)

#### Clone the Repository

```bash
git clone https://github.com/yourusername/ttsbook.git
cd ttsbook
```

#### Create a Virtual Environment

It's recommended to use a virtual environment to avoid conflicts with other packages:

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

#### Install Dependencies

```bash
# Install in development mode with all dependencies
pip install -e ".[dev,docs]"

# Or install just the basic package
pip install -e .
```

## Verify Installation

Test that TTSBook is installed correctly:

```bash
# Test the command line interface
ttsbook --help

# Test the Python import
python -c "import ttsbook; print(ttsbook.__version__)"
```

## Development Setup

If you plan to contribute to TTSBook, install the development dependencies:

```bash
# Install development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks (optional but recommended)
pre-commit install
```

### Running Tests

```bash
# Run all tests
pytest

# Run tests with coverage
pytest --cov=src --cov-report=html
```

### Code Formatting

```bash
# Format code
black src/ tests/

# Check code style
flake8 src/ tests/

# Type checking
mypy src/
```

## Troubleshooting

### Common Issues

1. **Permission Errors**: Use `--user` flag with pip or use a virtual environment
2. **Python Version**: Ensure you're using Python 3.8 or higher
3. **Missing Dependencies**: Try upgrading pip: `pip install --upgrade pip`

### Getting Help

If you encounter issues:

1. Check the [GitHub Issues](https://github.com/yourusername/ttsbook/issues)
2. Create a new issue with details about your problem
3. Include your Python version, OS, and error messages

## Optional Dependencies

TTSBook has optional dependencies for extended functionality:

```bash
# For advanced TTS features
pip install "ttsbook[tts]"

# For all optional features
pip install "ttsbook[all]"
```
