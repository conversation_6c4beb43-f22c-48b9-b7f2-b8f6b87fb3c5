# API Documentation

This document provides detailed information about the TTSBook API.

## Core Classes

### TTSBook

The main application class that handles text-to-speech book processing.

#### Constructor

```python
TTSBook(verbose: bool = False)
```

**Parameters:**
- `verbose` (bool): Enable verbose logging output. Default: False

**Example:**
```python
from ttsbook import TTSBook

# Create instance with default settings
app = TTSBook()

# Create instance with verbose logging
app = TTSBook(verbose=True)
```

#### Methods

##### process_text

```python
process_text(text: str) -> str
```

Process text for TTS conversion.

**Parameters:**
- `text` (str): Input text to process

**Returns:**
- `str`: Processed text ready for TTS conversion

**Example:**
```python
app = TTSBook()
processed = app.process_text("  Hello, world!  ")
print(processed)  # Output: "Hello, world!"
```

##### run

```python
run() -> None
```

Run the main application logic.

**Example:**
```python
app = TTSBook()
app.run()
```

## Command Line Interface

### Main Command

```bash
ttsbook [OPTIONS]
```

**Options:**
- `--verbose, -v`: Enable verbose output
- `--input-file, -i PATH`: Input text file to process
- `--output-dir, -o PATH`: Output directory for audio files
- `--help`: Show help message and exit

**Examples:**

```bash
# Basic usage
ttsbook

# With verbose output
ttsbook --verbose

# Process a specific file
ttsbook --input-file book.txt --output-dir ./audio/

# Show help
ttsbook --help
```

## Module Structure

### ttsbook.main

Contains the main application logic and CLI interface.

**Functions:**
- `main()`: Main CLI entry point

**Classes:**
- `TTSBook`: Main application class

## Error Handling

The API uses standard Python exceptions:

- `FileNotFoundError`: When input files cannot be found
- `PermissionError`: When output directories cannot be created
- `ValueError`: When invalid parameters are provided

## Logging

TTSBook uses Python's standard logging module. Log levels:

- `INFO`: General information about processing
- `DEBUG`: Detailed debugging information (enabled with verbose mode)
- `WARNING`: Warning messages
- `ERROR`: Error messages

**Example:**
```python
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)

# Use TTSBook with logging
app = TTSBook(verbose=True)
app.run()
```

## Type Hints

TTSBook is fully typed using Python type hints. Import types:

```python
from typing import Optional, List, Dict
```

## Future API Extensions

Planned additions to the API:

- Audio format selection
- Voice configuration
- Batch processing
- Progress callbacks
- Custom text processors
