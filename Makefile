.PHONY: help install install-dev test test-cov lint format clean build docs

help:
	@echo "Available commands:"
	@echo "  install     Install the package"
	@echo "  install-dev Install the package in development mode with dev dependencies"
	@echo "  test        Run tests"
	@echo "  test-cov    Run tests with coverage"
	@echo "  lint        Run linting (flake8, mypy)"
	@echo "  format      Format code (black)"
	@echo "  clean       Clean build artifacts"
	@echo "  build       Build the package"
	@echo "  docs        Build documentation"

install:
	pip install -e .

install-dev:
	pip install -e ".[dev,docs]"

test:
	python -m pytest tests/

test-cov:
	python -m pytest tests/ --cov=src --cov-report=html --cov-report=term-missing

lint:
	flake8 src/ tests/
	mypy src/

format:
	black src/ tests/

clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build: clean
	python -m build

docs:
	cd docs && make html
