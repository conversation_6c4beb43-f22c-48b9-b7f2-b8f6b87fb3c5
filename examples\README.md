# TTSBook Examples

This directory contains example scripts demonstrating how to use TTSBook.

## Available Examples

### basic_usage.py

Demonstrates the basic functionality of TTSBook:
- Creating a TTSBook instance
- Processing text
- Running the main application

**Usage:**
```bash
python examples/basic_usage.py
```

## Running Examples

Make sure TTSBook is installed before running examples:

```bash
# Install TTSBook in development mode
pip install -e .

# Run an example
python examples/basic_usage.py
```

## Example Structure

Each example follows this structure:
1. Import necessary modules
2. Demonstrate specific functionality
3. Provide clear output and explanations

## Contributing Examples

When adding new examples:
1. Create a descriptive filename
2. Include a docstring explaining the example
3. Add clear comments throughout the code
4. Update this README with the new example
5. Test the example thoroughly

## Common Use Cases

Future examples will cover:
- File processing
- Batch operations
- Custom configuration
- Error handling
- Integration with other tools
