Metadata-Version: 2.4
Name: ttsbook
Version: 0.1.0
Summary: A Python project for text-to-speech book processing
Home-page: https://github.com/yourusername/ttsbook
Author: Your Name
Author-email: Your Name <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/yourusername/ttsbook
Project-URL: Documentation, https://github.com/yourusername/ttsbook/docs
Project-URL: Repository, https://github.com/yourusername/ttsbook
Project-URL: Bug Tracker, https://github.com/yourusername/ttsbook/issues
Keywords: tts,text-to-speech,book,audio
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: requests>=2.28.0
Requires-Dist: click>=8.0.0
Requires-Dist: pydantic>=1.10.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=22.0.0; extra == "dev"
Requires-Dist: flake8>=5.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Requires-Dist: pre-commit>=2.20.0; extra == "dev"
Provides-Extra: docs
Requires-Dist: sphinx>=5.0.0; extra == "docs"
Requires-Dist: sphinx-rtd-theme>=1.0.0; extra == "docs"
Dynamic: author
Dynamic: home-page
Dynamic: license-file
Dynamic: requires-python

# TTSBook

A Python project for text-to-speech book processing.

## Description

This project provides tools and utilities for converting text books into audio format using text-to-speech technology.

## Installation

### From Source

1. Clone the repository:
```bash
git clone <repository-url>
cd ttsbook
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Install the package in development mode:
```bash
pip install -e .
```

## Usage

```python
from ttsbook import main

# Example usage
main.run()
```

## Development

### Running Tests

```bash
python -m pytest tests/
```

### Code Formatting

```bash
black src/ tests/
flake8 src/ tests/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for your changes
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Documentation

For detailed documentation, see the [docs](docs/) directory.
