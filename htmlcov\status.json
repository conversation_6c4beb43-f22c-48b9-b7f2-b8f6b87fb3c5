{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.0", "globals": "6f30a0718e338a5a72e8b8b961bc652c", "files": {"z_8ce4f148639a6918___init___py": {"hash": "516a6eb8fbbdf0220995e6cbb2105657", "index": {"url": "z_8ce4f148639a6918___init___py.html", "file": "src\\ttsbook\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8ce4f148639a6918_main_py": {"hash": "c63c4390bc02b169fc842b6689178b02", "index": {"url": "z_8ce4f148639a6918_main_py.html", "file": "src\\ttsbook\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}