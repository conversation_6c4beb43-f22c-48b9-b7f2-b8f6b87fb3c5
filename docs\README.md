# TTSBook Documentation

Welcome to the TTSBook documentation. This directory contains comprehensive documentation for the TTSBook project.

## Contents

- [Installation Guide](installation.md) - How to install and set up TTSBook
- [API Documentation](api.md) - Detailed API reference
- [Examples](../examples/) - Usage examples and tutorials

## Quick Start

1. Install TTSBook following the [installation guide](installation.md)
2. Check out the [examples](../examples/) to see TTSBook in action
3. Refer to the [API documentation](api.md) for detailed usage information

## Project Structure

```
ttsbook/
├── src/ttsbook/          # Main package source code
├── tests/                # Test suite
├── docs/                 # Documentation (you are here)
├── examples/             # Usage examples
├── scripts/              # Utility scripts
├── requirements.txt      # Python dependencies
├── setup.py             # Package setup
├── pyproject.toml       # Modern Python project configuration
└── README.md            # Project overview
```

## Contributing to Documentation

Documentation is written in Markdown format. To contribute:

1. Edit the relevant `.md` files
2. Ensure all links work correctly
3. Test any code examples
4. Submit a pull request

## Building Documentation

If using Sphinx for documentation generation:

```bash
cd docs
make html
```

The generated documentation will be available in `docs/_build/html/`.
