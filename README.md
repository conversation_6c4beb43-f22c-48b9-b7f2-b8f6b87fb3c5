# TTSBook

A Python project for text-to-speech book processing.

## Description

This project provides tools and utilities for converting text books into audio format using text-to-speech technology.

## Installation

### From Source

1. Clone the repository:
```bash
git clone <repository-url>
cd ttsbook
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Install the package in development mode:
```bash
pip install -e .
```

## Usage

```python
from ttsbook import main

# Example usage
main.run()
```

## Development

### Running Tests

```bash
python -m pytest tests/
```

### Code Formatting

```bash
black src/ tests/
flake8 src/ tests/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for your changes
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Documentation

For detailed documentation, see the [docs](docs/) directory.
