#!/usr/bin/env python3
"""Chatterbox AI integration for TTSBook."""

import logging
import os
from pathlib import Path
from typing import Op<PERSON>, Tu<PERSON>, Union
import torch
import torchaudio as ta

logger = logging.getLogger(__name__)


class ChatterboxIntegration:
    """Integration class for Chatterbox TTS in TTSBook."""
    
    def __init__(self, device: str = "auto", cache_dir: Optional[str] = None):
        """Initialize Chatterbox TTS integration.
        
        Args:
            device: Device to use ('auto', 'cuda', 'mps', 'cpu')
            cache_dir: Optional cache directory for models
        """
        self.device = self._detect_device() if device == "auto" else device
        self.model = None
        self.vc_model = None
        self.cache_dir = cache_dir
        
        logger.info(f"Initializing Chatterbox TTS on device: {self.device}")
        
    def _detect_device(self) -> str:
        """Automatically detect the best available device."""
        if torch.cuda.is_available():
            return "cuda"
        elif torch.backends.mps.is_available():
            return "mps"
        return "cpu"
    
    def _load_tts_model(self):
        """Lazy load the TTS model."""
        if self.model is None:
            try:
                from chatterbox.tts import ChatterboxTTS
                logger.info("Loading Chatterbox TTS model...")
                self.model = ChatterboxTTS.from_pretrained(device=self.device)
                logger.info("Chatterbox TTS model loaded successfully")
            except ImportError:
                raise ImportError(
                    "chatterbox-tts not installed. Install with: pip install chatterbox-tts"
                )
            except Exception as e:
                logger.error(f"Failed to load Chatterbox TTS model: {e}")
                raise
    
    def _load_vc_model(self):
        """Lazy load the voice conversion model."""
        if self.vc_model is None:
            try:
                from chatterbox.vc import ChatterboxVC
                logger.info("Loading Chatterbox VC model...")
                self.vc_model = ChatterboxVC.from_pretrained(device=self.device)
                logger.info("Chatterbox VC model loaded successfully")
            except ImportError:
                raise ImportError(
                    "chatterbox-tts not installed. Install with: pip install chatterbox-tts"
                )
            except Exception as e:
                logger.error(f"Failed to load Chatterbox VC model: {e}")
                raise
    
    def generate_speech(
        self,
        text: str,
        voice_path: Optional[str] = None,
        exaggeration: float = 0.5,
        cfg_weight: float = 0.5,
        temperature: float = 0.8,
        **kwargs
    ) -> Tuple[torch.Tensor, int]:
        """Generate speech from text using Chatterbox TTS.
        
        Args:
            text: Input text to synthesize
            voice_path: Optional path to reference audio for voice cloning
            exaggeration: Emotion intensity control (0.0-1.0)
            cfg_weight: Classifier-free guidance weight (0.0-1.0)
            temperature: Sampling temperature for generation
            **kwargs: Additional parameters
            
        Returns:
            Tuple of (audio_tensor, sample_rate)
        """
        self._load_tts_model()
        
        if not text or not text.strip():
            raise ValueError("Text cannot be empty")
        
        logger.info(f"Generating speech for text: '{text[:50]}...'")
        
        try:
            with torch.inference_mode():
                wav = self.model.generate(
                    text=text,
                    audio_prompt_path=voice_path,
                    exaggeration=exaggeration,
                    cfg_weight=cfg_weight,
                    temperature=temperature,
                    **kwargs
                )
            
            logger.info("Speech generation completed successfully")
            return wav, self.model.sr
            
        except torch.cuda.OutOfMemoryError:
            logger.error("GPU out of memory during speech generation")
            torch.cuda.empty_cache()
            raise RuntimeError("GPU out of memory. Try shorter text or use CPU.")
        except Exception as e:
            logger.error(f"Speech generation failed: {e}")
            raise
    
    def convert_voice(
        self,
        source_audio_path: str,
        target_voice_path: str
    ) -> Tuple[torch.Tensor, int]:
        """Convert voice characteristics of source audio.
        
        Args:
            source_audio_path: Path to source audio file
            target_voice_path: Path to target voice reference
            
        Returns:
            Tuple of (converted_audio_tensor, sample_rate)
        """
        self._load_vc_model()
        
        if not os.path.exists(source_audio_path):
            raise FileNotFoundError(f"Source audio not found: {source_audio_path}")
        if not os.path.exists(target_voice_path):
            raise FileNotFoundError(f"Target voice not found: {target_voice_path}")
        
        logger.info(f"Converting voice: {source_audio_path} -> {target_voice_path}")
        
        try:
            with torch.inference_mode():
                wav = self.vc_model.generate(
                    audio=source_audio_path,
                    target_voice_path=target_voice_path
                )
            
            logger.info("Voice conversion completed successfully")
            return wav, self.vc_model.sr
            
        except Exception as e:
            logger.error(f"Voice conversion failed: {e}")
            raise
    
    def save_audio(
        self,
        audio_tensor: torch.Tensor,
        output_path: Union[str, Path],
        sample_rate: int
    ) -> None:
        """Save audio tensor to file.
        
        Args:
            audio_tensor: Audio data as tensor
            output_path: Output file path
            sample_rate: Audio sample rate
        """
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            ta.save(str(output_path), audio_tensor, sample_rate)
            logger.info(f"Audio saved to: {output_path}")
        except Exception as e:
            logger.error(f"Failed to save audio: {e}")
            raise
    
    def process_text_to_audio(
        self,
        text: str,
        output_path: Union[str, Path],
        voice_path: Optional[str] = None,
        **generation_kwargs
    ) -> str:
        """Complete pipeline: text to audio file.
        
        Args:
            text: Input text to synthesize
            output_path: Output audio file path
            voice_path: Optional voice reference for cloning
            **generation_kwargs: Additional generation parameters
            
        Returns:
            Path to generated audio file
        """
        # Generate speech
        wav, sr = self.generate_speech(text, voice_path, **generation_kwargs)
        
        # Save to file
        self.save_audio(wav, output_path, sr)
        
        return str(output_path)
    
    def get_device_info(self) -> dict:
        """Get information about the current device setup.
        
        Returns:
            Dictionary with device information
        """
        info = {
            "device": self.device,
            "cuda_available": torch.cuda.is_available(),
            "mps_available": torch.backends.mps.is_available(),
        }
        
        if torch.cuda.is_available():
            info.update({
                "cuda_device_count": torch.cuda.device_count(),
                "cuda_device_name": torch.cuda.get_device_name(0),
                "cuda_memory_total": torch.cuda.get_device_properties(0).total_memory,
            })
        
        return info
    
    def clear_cache(self) -> None:
        """Clear GPU cache to free memory."""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            logger.info("GPU cache cleared")
    
    def __del__(self):
        """Cleanup when object is destroyed."""
        self.clear_cache()


# Convenience functions for easy integration
def create_chatterbox_tts(device: str = "auto") -> ChatterboxIntegration:
    """Create a Chatterbox TTS integration instance.
    
    Args:
        device: Device to use for inference
        
    Returns:
        ChatterboxIntegration instance
    """
    return ChatterboxIntegration(device=device)


def quick_tts(
    text: str,
    output_path: str,
    voice_path: Optional[str] = None,
    device: str = "auto"
) -> str:
    """Quick text-to-speech generation.
    
    Args:
        text: Text to synthesize
        output_path: Output audio file path
        voice_path: Optional voice reference
        device: Device to use
        
    Returns:
        Path to generated audio file
    """
    tts = create_chatterbox_tts(device)
    return tts.process_text_to_audio(text, output_path, voice_path)


# Example usage and testing
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Create TTS instance
    tts = ChatterboxIntegration()
    
    # Print device info
    print("Device Info:", tts.get_device_info())
    
    # Example text
    sample_text = "Hello, this is Chatterbox TTS integrated with TTSBook!"
    
    try:
        # Generate speech
        wav, sr = tts.generate_speech(sample_text)
        print(f"Generated audio: shape={wav.shape}, sample_rate={sr}")
        
        # Save to file
        output_file = "test_chatterbox_output.wav"
        tts.save_audio(wav, output_file, sr)
        print(f"Audio saved to: {output_file}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        tts.clear_cache()
