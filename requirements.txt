# Build system dependencies
setuptools>=61.0
wheel>=0.37.0
build>=0.8.0

# Core dependencies
requests>=2.28.0
click>=8.0.0
pydantic>=1.10.0

# Development dependencies
pytest>=7.0.0
pytest-cov>=4.0.0
black>=22.0.0
flake8>=5.0.0
mypy>=1.0.0
pre-commit>=2.20.0

# Documentation
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0

# Chatterbox TTS dependencies
chatterbox-tts>=0.1.0
torch>=2.0.0
torchaudio>=2.0.0
librosa>=0.10.0
huggingface-hub>=0.16.0
safetensors>=0.3.0

# Audio processing
soundfile>=0.12.0
numpy>=1.21.0

# Optional dependencies for TTS functionality
# Uncomment as needed:
# pyttsx3>=2.90
# gTTS>=2.3.0
# speechrecognition>=3.10.0

