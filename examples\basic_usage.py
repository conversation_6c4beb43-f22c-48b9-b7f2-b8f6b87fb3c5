#!/usr/bin/env python3
"""Basic usage example for TTSBook."""

from ttsbook import TTSBook


def main():
    """Demonstrate basic TTSBook usage."""
    print("TTSBook Basic Usage Example")
    print("=" * 30)
    
    # Create TTSBook instance
    app = TTSBook(verbose=True)
    
    # Example text processing
    sample_texts = [
        "Hello, world!",
        "  This is a sample text with extra spaces.  ",
        "Another example for text-to-speech processing.",
        "",  # Empty text
        "   \n\t   ",  # Whitespace only
    ]
    
    print("\nProcessing sample texts:")
    for i, text in enumerate(sample_texts, 1):
        print(f"\nExample {i}:")
        print(f"Input:  '{text}'")
        processed = app.process_text(text)
        print(f"Output: '{processed}'")
    
    print("\nRunning main application:")
    app.run()


if __name__ == "__main__":
    main()
