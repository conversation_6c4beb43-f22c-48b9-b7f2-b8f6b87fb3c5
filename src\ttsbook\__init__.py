"""TTSBook - A Python project for text-to-speech book processing."""

__version__ = "0.1.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

from .main import main

# Optional Chatterbox AI integration
try:
    from .chatterbox_integration import ChatterboxIntegration, create_chatterbox_tts, quick_tts
    __all__ = ["main", "ChatterboxIntegration", "create_chatterbox_tts", "quick_tts"]
except ImportError:
    # Chatterbox TTS not available
    __all__ = ["main"]
