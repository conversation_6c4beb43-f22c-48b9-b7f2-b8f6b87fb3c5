# Scripts Directory

This directory contains utility scripts for the TTSBook project.

## Available Scripts

### setup_dev.py

Development environment setup script that:
- Checks Python version compatibility
- Installs the package in development mode
- Installs development dependencies
- Sets up pre-commit hooks
- Runs initial tests
- Checks code formatting

**Usage:**
```bash
python scripts/setup_dev.py
```

## Script Guidelines

When adding new scripts:

1. **Naming**: Use descriptive names with underscores
2. **Documentation**: Include docstrings and comments
3. **Error Handling**: Handle errors gracefully
4. **Cross-platform**: Consider Windows/macOS/Linux compatibility
5. **Dependencies**: Document any external dependencies

## Common Script Types

Future scripts might include:
- Build and release automation
- Database migration scripts
- Data processing utilities
- Deployment scripts
- Performance benchmarking
- Code generation tools

## Running Scripts

All scripts should be executable from the project root:

```bash
# Make script executable (Unix/macOS)
chmod +x scripts/script_name.py

# Run script
python scripts/script_name.py
```
