#!/usr/bin/env python3
"""Development environment setup script."""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed")
        print(f"Error: {e.stderr}")
        return False
    return True


def main():
    """Set up development environment."""
    print("TTSBook Development Environment Setup")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("pyproject.toml").exists():
        print("Error: Please run this script from the project root directory")
        sys.exit(1)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"Python version: {sys.version}")
    
    # Install package in development mode
    if not run_command("pip install -e .", "Installing package in development mode"):
        sys.exit(1)
    
    # Install development dependencies
    if not run_command("pip install -e \".[dev,docs]\"", "Installing development dependencies"):
        sys.exit(1)
    
    # Install pre-commit hooks (optional)
    print("\nSetting up pre-commit hooks (optional)...")
    if run_command("pre-commit install", "Installing pre-commit hooks"):
        print("✓ Pre-commit hooks installed")
    else:
        print("⚠ Pre-commit hooks installation failed (this is optional)")
    
    # Run tests to verify setup
    if not run_command("python -m pytest tests/ -v", "Running tests to verify setup"):
        print("⚠ Some tests failed, but setup is complete")
    
    # Check code formatting
    if not run_command("black --check src/ tests/", "Checking code formatting"):
        print("⚠ Code formatting check failed")
        print("Run 'black src/ tests/' to format code")
    
    print("\n" + "=" * 40)
    print("Development environment setup complete!")
    print("\nNext steps:")
    print("1. Run tests: pytest")
    print("2. Format code: black src/ tests/")
    print("3. Check linting: flake8 src/ tests/")
    print("4. Type checking: mypy src/")
    print("5. Start developing!")


if __name__ == "__main__":
    main()
