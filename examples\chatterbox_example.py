#!/usr/bin/env python3
"""Example demonstrating Chatterbox AI integration with TTSBook."""

import logging
import os
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def main():
    """Demonstrate Chatterbox TTS integration."""
    print("Chatterbox AI Integration Example")
    print("=" * 40)
    
    try:
        # Import Chatterbox integration
        from ttsbook.chatterbox_integration import ChatterboxIntegration
        
        # Create TTS instance
        print("\n1. Initializing Chatterbox TTS...")
        tts = ChatterboxIntegration(device="auto")
        
        # Display device information
        device_info = tts.get_device_info()
        print(f"Device: {device_info['device']}")
        print(f"CUDA Available: {device_info['cuda_available']}")
        print(f"MPS Available: {device_info['mps_available']}")
        
        if device_info['cuda_available']:
            print(f"GPU: {device_info.get('cuda_device_name', 'Unknown')}")
        
        # Create output directory
        output_dir = Path("./chatterbox_outputs")
        output_dir.mkdir(exist_ok=True)
        
        # Example 1: Basic text-to-speech
        print("\n2. Basic Text-to-Speech Generation...")
        basic_text = "Hello! This is Chatterbox TTS integrated with TTSBook. The quality is amazing!"
        
        try:
            wav, sr = tts.generate_speech(basic_text)
            output_file = output_dir / "basic_example.wav"
            tts.save_audio(wav, output_file, sr)
            print(f"✓ Basic TTS saved to: {output_file}")
        except Exception as e:
            print(f"✗ Basic TTS failed: {e}")
        
        # Example 2: Expressive speech with emotion control
        print("\n3. Expressive Speech Generation...")
        expressive_text = "Wow! This is absolutely incredible! I can't believe how natural this sounds!"
        
        try:
            wav, sr = tts.generate_speech(
                text=expressive_text,
                exaggeration=0.8,  # Higher emotion
                cfg_weight=0.3,    # Lower for more expressive speech
                temperature=0.9    # Slightly higher temperature
            )
            output_file = output_dir / "expressive_example.wav"
            tts.save_audio(wav, output_file, sr)
            print(f"✓ Expressive TTS saved to: {output_file}")
        except Exception as e:
            print(f"✗ Expressive TTS failed: {e}")
        
        # Example 3: Voice cloning (if reference audio is available)
        print("\n4. Voice Cloning Example...")
        reference_audio = "./reference_audio/sample_voice.wav"
        
        if os.path.exists(reference_audio):
            clone_text = "This is an example of voice cloning using Chatterbox TTS."
            try:
                wav, sr = tts.generate_speech(
                    text=clone_text,
                    voice_path=reference_audio,
                    exaggeration=0.5
                )
                output_file = output_dir / "voice_clone_example.wav"
                tts.save_audio(wav, output_file, sr)
                print(f"✓ Voice cloning saved to: {output_file}")
            except Exception as e:
                print(f"✗ Voice cloning failed: {e}")
        else:
            print(f"⚠ Reference audio not found: {reference_audio}")
            print("  Place a reference audio file there to test voice cloning")
        
        # Example 4: Batch processing
        print("\n5. Batch Processing Example...")
        batch_texts = [
            "This is the first sentence in our batch.",
            "Here's the second sentence with different content.",
            "And finally, the third sentence completes our batch processing demo."
        ]
        
        try:
            for i, text in enumerate(batch_texts):
                wav, sr = tts.generate_speech(text)
                output_file = output_dir / f"batch_{i+1:02d}.wav"
                tts.save_audio(wav, output_file, sr)
                print(f"✓ Batch {i+1}/3 saved to: {output_file}")
        except Exception as e:
            print(f"✗ Batch processing failed: {e}")
        
        # Example 5: Using the convenience function
        print("\n6. Quick TTS Function Example...")
        try:
            from ttsbook.chatterbox_integration import quick_tts
            
            quick_text = "This demonstrates the quick TTS convenience function."
            output_file = output_dir / "quick_example.wav"
            
            result_path = quick_tts(
                text=quick_text,
                output_path=str(output_file),
                device="auto"
            )
            print(f"✓ Quick TTS saved to: {result_path}")
        except Exception as e:
            print(f"✗ Quick TTS failed: {e}")
        
        # Example 6: Long text processing
        print("\n7. Long Text Processing...")
        long_text = """
        This is a longer text example that demonstrates how Chatterbox TTS handles 
        extended passages. The model maintains consistency and quality even with 
        longer inputs. This is particularly useful for book processing, where 
        chapters or sections might contain substantial amounts of text that need 
        to be converted to speech while maintaining natural flow and intonation.
        """
        
        try:
            wav, sr = tts.generate_speech(long_text.strip())
            output_file = output_dir / "long_text_example.wav"
            tts.save_audio(wav, output_file, sr)
            print(f"✓ Long text TTS saved to: {output_file}")
        except Exception as e:
            print(f"✗ Long text TTS failed: {e}")
        
        # Clean up
        print("\n8. Cleaning up...")
        tts.clear_cache()
        print("✓ GPU cache cleared")
        
        print(f"\n🎉 All examples completed!")
        print(f"📁 Output files saved in: {output_dir.absolute()}")
        print("\nNext steps:")
        print("- Listen to the generated audio files")
        print("- Experiment with different parameters")
        print("- Try voice cloning with your own reference audio")
        print("- Integrate into your TTSBook workflows")
        
    except ImportError:
        print("❌ Chatterbox TTS not installed!")
        print("\nTo install Chatterbox TTS:")
        print("1. pip install chatterbox-tts")
        print("2. For GPU support: pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121")
        print("3. Run this example again")
        
    except Exception as e:
        logger.error(f"Example failed: {e}")
        print(f"\n❌ Example failed: {e}")
        print("\nTroubleshooting:")
        print("- Check that all dependencies are installed")
        print("- Ensure sufficient GPU memory (if using CUDA)")
        print("- Try running with CPU: device='cpu'")


def demonstrate_voice_conversion():
    """Demonstrate voice conversion capabilities."""
    print("\nVoice Conversion Example")
    print("=" * 30)
    
    try:
        from ttsbook.chatterbox_integration import ChatterboxIntegration
        
        # This would require both source and target audio files
        source_audio = "./audio_samples/source.wav"
        target_voice = "./audio_samples/target_voice.wav"
        
        if os.path.exists(source_audio) and os.path.exists(target_voice):
            tts = ChatterboxIntegration()
            
            # Convert voice
            converted_wav, sr = tts.convert_voice(source_audio, target_voice)
            
            # Save result
            output_file = "./chatterbox_outputs/voice_conversion.wav"
            tts.save_audio(converted_wav, output_file, sr)
            
            print(f"✓ Voice conversion saved to: {output_file}")
        else:
            print("⚠ Voice conversion requires source and target audio files")
            print(f"  Source: {source_audio}")
            print(f"  Target: {target_voice}")
            
    except ImportError:
        print("❌ Chatterbox TTS not available for voice conversion")
    except Exception as e:
        print(f"❌ Voice conversion failed: {e}")


if __name__ == "__main__":
    main()
    demonstrate_voice_conversion()
