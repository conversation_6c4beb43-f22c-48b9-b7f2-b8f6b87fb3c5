# Chatterbox AI Integration Guide

## Overview

Chatterbox TTS is Resemble AI's first production-grade open-source Text-to-Speech model, licensed under MIT. It's a state-of-the-art zero-shot TTS system that consistently outperforms leading closed-source systems like ElevenLabs in side-by-side evaluations.

### Key Features

- **State-of-the-Art Zero-shot TTS**: High-quality speech synthesis from text
- **0.5B Llama Backbone**: Built on proven transformer architecture
- **Emotion Exaggeration Control**: Unique feature for expressive speech
- **Ultra-stable Generation**: Alignment-informed inference for consistent output
- **Voice Cloning**: Clone voices from reference audio samples
- **Watermarked Outputs**: Built-in Perth watermarking for responsible AI
- **MIT Licensed**: Free for commercial and personal use

## Installation

### Basic Installation

```bash
pip install chatterbox-tts
```

### With PyTorch GPU Support

For CUDA-enabled GPU acceleration:

```bash
# Install PyTorch with CUDA support first
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121

# Then install Chatterbox TTS
pip install chatterbox-tts
```

### Additional Dependencies

```bash
pip install librosa soundfile numpy huggingface-hub safetensors
```

## Core API Reference

### ChatterboxTTS Class

The main class for text-to-speech generation.

#### Initialization

```python
from chatterbox.tts import ChatterboxTTS

# Load model from Hugging Face Hub
model = ChatterboxTTS.from_pretrained(device="cuda")  # or "cpu", "mps"

# Load from local checkpoint
model = ChatterboxTTS.from_local(ckpt_dir="./model", device="cuda")
```

#### Key Methods

##### `generate(text, audio_prompt_path=None, exaggeration=0.5, cfg_weight=0.5, temperature=0.8)`

Generate speech from text.

**Parameters:**
- `text` (str): Input text to synthesize
- `audio_prompt_path` (str, optional): Path to reference audio for voice cloning
- `exaggeration` (float, 0.0-1.0): Emotion intensity control (default: 0.5)
- `cfg_weight` (float, 0.0-1.0): Classifier-free guidance weight (default: 0.5)
- `temperature` (float): Sampling temperature for generation (default: 0.8)

**Returns:**
- `torch.Tensor`: Generated audio waveform

**Example:**
```python
import torchaudio as ta
from chatterbox.tts import ChatterboxTTS

model = ChatterboxTTS.from_pretrained(device="cuda")

# Basic text-to-speech
text = "Hello, this is Chatterbox TTS generating natural speech."
wav = model.generate(text)
ta.save("output.wav", wav, model.sr)

# Voice cloning with reference audio
wav = model.generate(text, audio_prompt_path="reference_voice.wav")
ta.save("cloned_voice.wav", wav, model.sr)
```

##### `prepare_conditionals(wav_fpath, exaggeration=0.5)`

Prepare voice conditioning from reference audio.

**Parameters:**
- `wav_fpath` (str): Path to reference audio file
- `exaggeration` (float): Emotion exaggeration level

**Example:**
```python
# Prepare voice conditioning
model.prepare_conditionals("reference.wav", exaggeration=0.7)

# Generate with prepared conditioning
wav = model.generate("This will use the prepared voice.")
```

### ChatterboxVC Class

Voice conversion functionality.

#### Initialization

```python
from chatterbox.vc import ChatterboxVC

model = ChatterboxVC.from_pretrained(device="cuda")
```

#### Key Methods

##### `generate(audio, target_voice_path=None)`

Convert voice characteristics of input audio.

**Parameters:**
- `audio` (str): Path to source audio file
- `target_voice_path` (str, optional): Path to target voice reference

**Returns:**
- `torch.Tensor`: Converted audio waveform

**Example:**
```python
import torchaudio as ta
from chatterbox.vc import ChatterboxVC

model = ChatterboxVC.from_pretrained(device="cuda")

# Voice conversion
converted_wav = model.generate("source_audio.wav", target_voice_path="target_voice.wav")
ta.save("converted_output.wav", converted_wav, model.sr)
```

##### `set_target_voice(wav_fpath)`

Set target voice for subsequent conversions.

**Parameters:**
- `wav_fpath` (str): Path to target voice reference audio

## Advanced Configuration

### Device Selection

Chatterbox TTS supports multiple compute devices:

```python
# Automatic device detection
import torch

if torch.cuda.is_available():
    device = "cuda"
elif torch.backends.mps.is_available():  # Apple Silicon
    device = "mps"
else:
    device = "cpu"

model = ChatterboxTTS.from_pretrained(device=device)
```

### Parameter Tuning Guidelines

#### General Use (TTS and Voice Agents)
- Default settings (`exaggeration=0.5`, `cfg_weight=0.5`) work well for most prompts
- For fast-speaking reference speakers, lower `cfg_weight` to ~0.3 for better pacing

#### Expressive or Dramatic Speech
- Use lower `cfg_weight` values (~0.3) 
- Increase `exaggeration` to 0.7 or higher
- Higher exaggeration speeds up speech; lower cfg_weight compensates with slower pacing

### Audio Format Support

Chatterbox TTS works with common audio formats:
- WAV (recommended for reference audio)
- MP3
- FLAC
- Other formats supported by librosa

## Integration Patterns

### Basic TTS Service

```python
class TTSService:
    def __init__(self, device="cuda"):
        self.model = ChatterboxTTS.from_pretrained(device=device)
        self.sr = self.model.sr
    
    def synthesize(self, text, voice_path=None, **kwargs):
        """Synthesize speech from text."""
        wav = self.model.generate(text, audio_prompt_path=voice_path, **kwargs)
        return wav.numpy(), self.sr
    
    def clone_voice(self, text, reference_audio):
        """Generate speech with voice cloning."""
        return self.synthesize(text, voice_path=reference_audio)
```

### Batch Processing

```python
def batch_synthesize(model, texts, output_dir="./outputs"):
    """Process multiple texts in batch."""
    import os
    import torchaudio as ta
    
    os.makedirs(output_dir, exist_ok=True)
    
    for i, text in enumerate(texts):
        wav = model.generate(text)
        output_path = os.path.join(output_dir, f"output_{i:03d}.wav")
        ta.save(output_path, wav, model.sr)
        print(f"Saved: {output_path}")
```

### Streaming Integration

```python
def stream_tts(model, text_stream):
    """Process streaming text input."""
    for text_chunk in text_stream:
        if text_chunk.strip():  # Skip empty chunks
            wav = model.generate(text_chunk)
            yield wav.numpy(), model.sr
```

## Performance Optimization

### Memory Management

```python
import torch

# Clear GPU cache periodically
torch.cuda.empty_cache()

# Use inference mode for better performance
with torch.inference_mode():
    wav = model.generate(text)
```

### Preprocessing Text

```python
def preprocess_text(text):
    """Clean and normalize text for better TTS results."""
    # The model includes built-in text normalization
    # Additional preprocessing can be added here
    text = text.strip()
    
    # Handle special cases
    if not text:
        return "You need to add some text for me to talk."
    
    return text
```

## Error Handling

### Common Issues and Solutions

```python
def safe_generate(model, text, **kwargs):
    """Generate speech with error handling."""
    try:
        # Validate inputs
        if not text or not text.strip():
            raise ValueError("Text cannot be empty")
        
        # Generate audio
        wav = model.generate(text, **kwargs)
        return wav, None
        
    except torch.cuda.OutOfMemoryError:
        torch.cuda.empty_cache()
        return None, "GPU out of memory. Try shorter text or use CPU."
        
    except FileNotFoundError as e:
        return None, f"Audio file not found: {e}"
        
    except Exception as e:
        return None, f"Generation failed: {e}"
```

### Device Compatibility

```python
def check_device_compatibility():
    """Check device compatibility and suggest optimal settings."""
    import torch
    
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        memory_gb = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"CUDA GPU: {gpu_name} ({memory_gb:.1f}GB)")
        return "cuda"
    elif torch.backends.mps.is_available():
        print("Apple Silicon MPS available")
        return "mps"
    else:
        print("Using CPU (slower but compatible)")
        return "cpu"
```

## Best Practices

### 1. Voice Reference Quality
- Use high-quality, clean audio (16kHz or higher)
- 3-10 seconds of speech is typically sufficient
- Avoid background noise and music
- Single speaker recordings work best

### 2. Text Preparation
- Use proper punctuation for natural pauses
- Avoid extremely long sentences
- The model handles most text normalization automatically

### 3. Parameter Selection
- Start with default parameters and adjust based on results
- Higher exaggeration for more expressive speech
- Lower cfg_weight for faster-paced speech

### 4. Resource Management
- Load model once and reuse for multiple generations
- Clear GPU cache if processing many files
- Use appropriate batch sizes for your hardware

## Troubleshooting

### Installation Issues
- Ensure PyTorch is installed with appropriate CUDA support
- Check that all audio dependencies (librosa, soundfile) are installed
- Verify Python version compatibility (3.8+)

### Runtime Issues
- **Out of memory**: Reduce batch size or use CPU
- **Slow generation**: Ensure GPU acceleration is working
- **Poor audio quality**: Check reference audio quality and parameters

### Model Loading Issues
- Verify internet connection for Hugging Face Hub downloads
- Check available disk space for model cache
- Ensure proper permissions for cache directory

## Integration with TTSBook

To integrate Chatterbox AI with your TTSBook project:

```python
# In src/ttsbook/chatterbox_integration.py
from chatterbox.tts import ChatterboxTTS
import torchaudio as ta

class ChatterboxIntegration:
    def __init__(self, device="auto"):
        if device == "auto":
            device = self._detect_device()
        self.model = ChatterboxTTS.from_pretrained(device=device)
    
    def _detect_device(self):
        import torch
        if torch.cuda.is_available():
            return "cuda"
        elif torch.backends.mps.is_available():
            return "mps"
        return "cpu"
    
    def generate_speech(self, text, voice_path=None, **kwargs):
        """Generate speech using Chatterbox TTS."""
        wav = self.model.generate(text, audio_prompt_path=voice_path, **kwargs)
        return wav, self.model.sr
    
    def save_audio(self, wav, output_path):
        """Save generated audio to file."""
        ta.save(output_path, wav, self.model.sr)
```

This integration provides a clean interface for using Chatterbox AI within your TTSBook project while maintaining compatibility with your existing architecture.
